<?php

use models\Database2;

global $LMRId;

// Fetch loan file data from tblFile and related tables
$summaryData = [];
try {
    $qry = "
        SELECT
            tf.propertyAddress,
            tf.propertyCity,
            tf.propertyState,
            tf.propertyZip,
            tfr.totalLoanAmount,
            tfhni.initialLoanAmount,
            tfhni.currentLoanBalance,
            tfhni.rehabCostFinanced,
            tfhni.loanTermExpireDate as maturityDate,
            tfcv.SimpleARV,
            tfcv.FullARV,
            tfcv.TotalProjectCost,
            tf.closedDate as closingDate
        FROM tblFile tf
        LEFT JOIN tblFileResponse tfr ON tf.LMRId = tfr.LMRId
        LEFT JOIN tblFileHMLONewLoanInfo tfhni ON tf.LMRId = tfhni.fileID
        LEFT JOIN tblFileCalculatedValues tfcv ON tf.LMRId = tfcv.LMRId
        WHERE tf.LMRId = :LMRId
    ";

    $result = Database2::getInstance()->queryData($qry, ['LMRId' => $LMRId]);
    $summaryData = $result[0] ?? [];
} catch (Exception $e) {
    error_log("Error fetching summary data: " . $e->getMessage());
}

// Format data with fallback values
$address = $summaryData['propertyAddress'] ?? '123 Main St';
$city = $summaryData['propertyCity'] ?? 'Dallas';
$state = $summaryData['propertyState'] ?? 'TX';
$zip = $summaryData['propertyZip'] ?? '75201';

$initialLoan = $summaryData['initialLoanAmount'] ?? 250000;
$rehabCostFinanced = $summaryData['rehabCostFinanced'] ?? 30000;
$totalLoanAmount = $summaryData['totalLoanAmount'] ?? 280000;
$currentLoanBalance = $summaryData['currentLoanBalance'] ?? 170000;
$rehabCost = $summaryData['TotalProjectCost'] ?? 40000;
$arv = $summaryData['SimpleARV'] ?? $summaryData['FullARV'] ?? 350000;

$closingDate = $summaryData['closingDate'] ?? '2025-03-01';
$maturityDate = $summaryData['maturityDate'] ?? '2025-09-01';

// Placeholder values as requested
$totalDrawsFunded = '$27,000';
$holdbackRemaining = '$13,000';
$dateOfLastDraw = '2025-05-12';
$dateOfCurrentDraw = '2025-05-30';

?>

<style>
.summary-section {
    background: #fff;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-section h4 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: start;
}

.summary-item {
    display: flex;
    flex-direction: column;
}

.summary-label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-value {
    font-size: 1rem;
    color: #495057;
    font-weight: 500;
}

.summary-value.currency {
    color: #28a745;
    font-weight: 600;
}

.summary-value.date {
    color: #007bff;
}

@media (max-width: 768px) {
    .summary-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<div class="summary-section">
    <h4>Summary</h4>

    <div class="summary-grid">
        
        <div class="summary-item">
            <div class="summary-label">Address</div>
            <div class="summary-value"><?= htmlspecialchars($address); ?></div>
        </div>

        <div class="summary-item">
            <div class="summary-label">City</div>
            <div class="summary-value"><?= htmlspecialchars($city); ?></div>
        </div>

        <div class="summary-item">
            <div class="summary-label">State</div>
            <div class="summary-value"><?= htmlspecialchars($state); ?></div>
        </div>

        <div class="summary-item">
            <div class="summary-label">Zip</div>
            <div class="summary-value"><?= htmlspecialchars($zip); ?></div>
        </div>

        <!-- Loan Information -->
        <div class="summary-item">
            <div class="summary-label">Initial Loan</div>
            <div class="summary-value currency">$<?= number_format($initialLoan); ?></div>
        </div>

        <div class="summary-item">
            <div class="summary-label">Rehab Cost Financed</div>
            <div class="summary-value currency">$<?= number_format($rehabCostFinanced); ?></div>
        </div>

        <div class="summary-item">
            <div class="summary-label">Total Loan Amount</div>
            <div class="summary-value currency">$<?= number_format($totalLoanAmount); ?></div>
        </div>

        <div class="summary-item">
            <div class="summary-label">Current Loan Balance</div>
            <div class="summary-value currency">$<?= number_format($currentLoanBalance); ?></div>
        </div>

        <div class="summary-item">
            <div class="summary-label">Rehab Cost</div>
            <div class="summary-value currency">$<?= number_format($rehabCost); ?></div>
        </div>

        <div class="summary-item">
            <div class="summary-label">ARV</div>
            <div class="summary-value currency">$<?= number_format($arv); ?></div>
        </div>

        <!-- Placeholder Values -->
        <div class="summary-item">
            <div class="summary-label">Total Draws Funded</div>
            <div class="summary-value currency"><?= htmlspecialchars($totalDrawsFunded); ?></div>
        </div>

        <div class="summary-item">
            <div class="summary-label">Holdback Remaining</div>
            <div class="summary-value currency"><?= htmlspecialchars($holdbackRemaining); ?></div>
        </div>

        <!-- Date Information -->
        <div class="summary-item">
            <div class="summary-label">Closing Date</div>
            <div class="summary-value date"><?= htmlspecialchars($closingDate); ?></div>
        </div>

        <div class="summary-item">
            <div class="summary-label">Maturity Date</div>
            <div class="summary-value date"><?= htmlspecialchars($maturityDate); ?></div>
        </div>

        <div class="summary-item">
            <div class="summary-label">Date of Last Draw</div>
            <div class="summary-value date"><?= htmlspecialchars($dateOfLastDraw); ?></div>
        </div>

        <div class="summary-item">
            <div class="summary-label">Date of Current Draw</div>
            <div class="summary-value date"><?= htmlspecialchars($dateOfCurrentDraw); ?></div>
        </div>
    </div>
</div>
